import '../errors/failures.dart';

/// A generic result type that represents either a success or a failure
sealed class Result<T> {
  const Result();
  
  /// Returns true if this is a success result
  bool get isSuccess => this is Success<T>;
  
  /// Returns true if this is a failure result
  bool get isFailure => this is Failure<T>;
  
  /// Returns the success value or null if this is a failure
  T? get successValue => switch (this) {
    Success<T> success => success.value,
    Failure<T> _ => null,
  };
  
  /// Returns the failure or null if this is a success
  Failure? get failureValue => switch (this) {
    Success<T> _ => null,
    Failure<T> failure => failure.failure,
  };
  
  /// Transforms the success value using the provided function
  Result<R> map<R>(R Function(T) transform) {
    return switch (this) {
      Success<T> success => Success(transform(success.value)),
      Failure<T> failure => Failure(failure.failure),
    };
  }
  
  /// Transforms the success value using the provided function that returns a Result
  Result<R> flatMap<R>(Result<R> Function(T) transform) {
    return switch (this) {
      Success<T> success => transform(success.value),
      Failure<T> failure => Failure(failure.failure),
    };
  }
  
  /// Executes the appropriate callback based on the result type
  R fold<R>({
    required R Function(T) onSuccess,
    required R Function(Failure) onFailure,
  }) {
    return switch (this) {
      Success<T> success => onSuccess(success.value),
      Failure<T> failure => onFailure(failure.failure),
    };
  }
}

/// Represents a successful result
final class Success<T> extends Result<T> {
  final T value;
  
  const Success(this.value);
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Success<T> && other.value == value;
  }
  
  @override
  int get hashCode => value.hashCode;
  
  @override
  String toString() => 'Success($value)';
}

/// Represents a failed result
final class Failure<T> extends Result<T> {
  final Failure failure;
  
  const Failure(this.failure);
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Failure<T> && other.failure == failure;
  }
  
  @override
  int get hashCode => failure.hashCode;
  
  @override
  String toString() => 'Failure($failure)';
}
