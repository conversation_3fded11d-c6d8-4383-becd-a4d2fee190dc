import '../entities/food_entry.dart';
import '../../core/utils/result.dart';
import '../../core/errors/failures.dart';

abstract class FoodEntryRepository {
  Future<Result<FoodEntry>> createFoodEntry(FoodEntry entry);
  Future<Result<FoodEntry?>> getFoodEntry(String entryId);
  Future<Result<List<FoodEntry>>> getFoodEntriesByUser(String userId);
  Future<Result<List<FoodEntry>>> getFoodEntriesByDate(String userId, DateTime date);
  Future<Result<List<FoodEntry>>> getFoodEntriesByDateRange(
    String userId, 
    DateTime startDate, 
    DateTime endDate,
  );
  Future<Result<List<FoodEntry>>> getFoodEntriesByMealType(
    String userId, 
    DateTime date, 
    String mealType,
  );
  Future<Result<FoodEntry>> updateFoodEntry(FoodEntry entry);
  Future<Result<void>> deleteFoodEntry(String entryId);
  Future<Result<List<FoodEntry>>> searchFoodEntries(String userId, String query);
}
