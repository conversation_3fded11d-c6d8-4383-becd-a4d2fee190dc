import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/user_profile.dart';
import '../../domain/repositories/user_profile_repository.dart';

class AppProvider extends ChangeNotifier {
  final UserProfileRepository _userProfileRepository;
  final SharedPreferences _prefs;

  AppProvider(this._userProfileRepository, this._prefs);

  bool _isFirstLaunch = true;
  bool _isLoading = false;
  UserProfile? _currentUser;
  String? _geminiApiKey;

  bool get isFirstLaunch => _isFirstLaunch;
  bool get isLoading => _isLoading;
  UserProfile? get currentUser => _currentUser;
  String? get geminiApiKey => _geminiApiKey;
  bool get hasApiKey => _geminiApiKey != null && _geminiApiKey!.isNotEmpty;

  Future<void> initialize() async {
    _setLoading(true);
    
    try {
      // Check if first launch
      _isFirstLaunch = _prefs.getBool(AppConstants.keyIsFirstLaunch) ?? true;
      
      // Load API key
      _geminiApiKey = _prefs.getString(AppConstants.keyGeminiApiKey);
      
      // Load current user if not first launch
      if (!_isFirstLaunch) {
        await _loadCurrentUser();
      }
    } catch (e) {
      debugPrint('Error initializing app: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _loadCurrentUser() async {
    try {
      final result = await _userProfileRepository.getCurrentUserProfile();
      if (result.isSuccess) {
        _currentUser = result.successValue;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading current user: $e');
    }
  }

  Future<void> completeOnboarding(UserProfile user) async {
    try {
      _currentUser = user;
      await _prefs.setBool(AppConstants.keyIsFirstLaunch, false);
      await _prefs.setString(AppConstants.keyUserId, user.id);
      _isFirstLaunch = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error completing onboarding: $e');
    }
  }

  Future<void> setGeminiApiKey(String apiKey) async {
    try {
      _geminiApiKey = apiKey;
      await _prefs.setString(AppConstants.keyGeminiApiKey, apiKey);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting API key: $e');
    }
  }

  Future<void> updateUserProfile(UserProfile user) async {
    try {
      _currentUser = user;
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating user profile: $e');
    }
  }

  Future<void> logout() async {
    try {
      _currentUser = null;
      _isFirstLaunch = true;
      await _prefs.clear();
      notifyListeners();
    } catch (e) {
      debugPrint('Error during logout: $e');
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
