import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

import 'core/constants/app_constants.dart';
import 'core/routes/route_generator.dart';
import 'core/routes/app_routes.dart';
import 'data/datasources/database_helper.dart';
import 'data/repositories/user_profile_repository_impl.dart';
import 'data/repositories/food_entry_repository_impl.dart';
import 'data/repositories/nutritional_goals_repository_impl.dart';
import 'domain/usecases/create_user_profile_usecase.dart';
import 'domain/usecases/get_daily_nutrition_usecase.dart';
import 'presentation/providers/app_provider.dart';
import 'presentation/providers/nutrition_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  final databaseHelper = DatabaseHelper();
  final httpClient = http.Client();

  // Initialize repositories
  final userProfileRepository = UserProfileRepositoryImpl(databaseHelper);
  final foodEntryRepository = FoodEntryRepositoryImpl(databaseHelper);
  final nutritionalGoalsRepository = NutritionalGoalsRepositoryImpl(
    databaseHelper,
  );

  // Initialize use cases
  final createUserProfileUseCase = CreateUserProfileUseCase(
    userProfileRepository,
    nutritionalGoalsRepository,
  );

  final getDailyNutritionUseCase = GetDailyNutritionUseCase(
    foodEntryRepository,
    nutritionalGoalsRepository,
  );

  runApp(
    NutriAIApp(
      sharedPreferences: sharedPreferences,
      userProfileRepository: userProfileRepository,
      foodEntryRepository: foodEntryRepository,
      nutritionalGoalsRepository: nutritionalGoalsRepository,
      createUserProfileUseCase: createUserProfileUseCase,
      getDailyNutritionUseCase: getDailyNutritionUseCase,
      httpClient: httpClient,
    ),
  );
}

class NutriAIApp extends StatelessWidget {
  final SharedPreferences sharedPreferences;
  final UserProfileRepositoryImpl userProfileRepository;
  final FoodEntryRepositoryImpl foodEntryRepository;
  final NutritionalGoalsRepositoryImpl nutritionalGoalsRepository;
  final CreateUserProfileUseCase createUserProfileUseCase;
  final GetDailyNutritionUseCase getDailyNutritionUseCase;
  final http.Client httpClient;

  const NutriAIApp({
    super.key,
    required this.sharedPreferences,
    required this.userProfileRepository,
    required this.foodEntryRepository,
    required this.nutritionalGoalsRepository,
    required this.createUserProfileUseCase,
    required this.getDailyNutritionUseCase,
    required this.httpClient,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => AppProvider(userProfileRepository, sharedPreferences),
        ),
        ChangeNotifierProvider(
          create: (_) =>
              NutritionProvider(getDailyNutritionUseCase, foodEntryRepository),
        ),
        Provider<CreateUserProfileUseCase>.value(
          value: createUserProfileUseCase,
        ),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: _buildTheme(),
        initialRoute: AppRoutes.splash,
        onGenerateRoute: RouteGenerator.generateRoute,
        debugShowCheckedModeBanner: false,
      ),
    );
  }

  ThemeData _buildTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF4CAF50), // Green theme for nutrition app
        brightness: Brightness.light,
      ),
      appBarTheme: const AppBarTheme(centerTitle: true, elevation: 0),
      cardTheme: CardThemeData(
        elevation: AppConstants.cardElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: 12,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: 12,
        ),
      ),
    );
  }
}
