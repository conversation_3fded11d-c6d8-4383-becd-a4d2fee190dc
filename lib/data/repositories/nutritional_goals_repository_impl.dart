import 'package:sqflite/sqflite.dart';
import '../../domain/entities/nutritional_goals.dart';
import '../../domain/repositories/nutritional_goals_repository.dart';
import '../../core/utils/result.dart';
import '../../core/errors/failures.dart';
import '../datasources/database_helper.dart';
import '../models/nutritional_goals_model.dart';

class NutritionalGoalsRepositoryImpl implements NutritionalGoalsRepository {
  final DatabaseHelper _databaseHelper;
  
  NutritionalGoalsRepositoryImpl(this._databaseHelper);
  
  @override
  Future<Result<NutritionalGoals>> createNutritionalGoals(NutritionalGoals goals) async {
    try {
      final db = await _databaseHelper.database;
      final model = NutritionalGoalsModel.fromEntity(goals);
      
      await db.insert(
        'nutritional_goals',
        model.toDatabase(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      return Success(goals);
    } catch (e) {
      return Failure(DatabaseFailure(message: 'Failed to create nutritional goals: $e'));
    }
  }
  
  @override
  Future<Result<NutritionalGoals?>> getNutritionalGoals(String userId) async {
    try {
      final db = await _databaseHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.query(
        'nutritional_goals',
        where: 'user_id = ?',
        whereArgs: [userId],
        orderBy: 'created_at DESC',
        limit: 1,
      );
      
      if (maps.isEmpty) {
        return const Success(null);
      }
      
      final model = NutritionalGoalsModel.fromDatabase(maps.first);
      return Success(model);
    } catch (e) {
      return Failure(DatabaseFailure(message: 'Failed to get nutritional goals: $e'));
    }
  }
  
  @override
  Future<Result<NutritionalGoals>> updateNutritionalGoals(NutritionalGoals goals) async {
    try {
      final db = await _databaseHelper.database;
      final model = NutritionalGoalsModel.fromEntity(goals);
      
      final count = await db.update(
        'nutritional_goals',
        model.toDatabase(),
        where: 'id = ?',
        whereArgs: [goals.id],
      );
      
      if (count == 0) {
        return Failure(DatabaseFailure(message: 'Nutritional goals not found'));
      }
      
      return Success(goals);
    } catch (e) {
      return Failure(DatabaseFailure(message: 'Failed to update nutritional goals: $e'));
    }
  }
  
  @override
  Future<Result<void>> deleteNutritionalGoals(String userId) async {
    try {
      final db = await _databaseHelper.database;
      
      final count = await db.delete(
        'nutritional_goals',
        where: 'user_id = ?',
        whereArgs: [userId],
      );
      
      if (count == 0) {
        return Failure(DatabaseFailure(message: 'Nutritional goals not found'));
      }
      
      return const Success(null);
    } catch (e) {
      return Failure(DatabaseFailure(message: 'Failed to delete nutritional goals: $e'));
    }
  }
}
