import 'dart:convert';
import '../../domain/entities/food_entry.dart';
import '../../domain/entities/nutrition_data.dart';

class FoodEntryModel extends FoodEntry {
  const FoodEntryModel({
    required super.id,
    required super.userId,
    required super.name,
    super.description,
    super.brand,
    required super.servingSize,
    required super.servingUnit,
    required super.nutritionData,
    super.imagePath,
    required super.mealType,
    required super.consumedAt,
    required super.createdAt,
    required super.updatedAt,
    super.notes,
    super.isAnalyzedByAI,
    super.confidence,
  });
  
  factory FoodEntryModel.fromEntity(FoodEntry entity) {
    return FoodEntryModel(
      id: entity.id,
      userId: entity.userId,
      name: entity.name,
      description: entity.description,
      brand: entity.brand,
      servingSize: entity.servingSize,
      servingUnit: entity.servingUnit,
      nutritionData: entity.nutritionData,
      imagePath: entity.imagePath,
      mealType: entity.mealType,
      consumedAt: entity.consumedAt,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      notes: entity.notes,
      isAnalyzedByAI: entity.isAnalyzedByAI,
      confidence: entity.confidence,
    );
  }
  
  factory FoodEntryModel.fromJson(Map<String, dynamic> json) {
    return FoodEntryModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      brand: json['brand'] as String?,
      servingSize: (json['serving_size'] as num).toDouble(),
      servingUnit: json['serving_unit'] as String,
      nutritionData: NutritionData.fromJson(json['nutrition_data'] as Map<String, dynamic>),
      imagePath: json['image_path'] as String?,
      mealType: json['meal_type'] as String,
      consumedAt: DateTime.parse(json['consumed_at'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      notes: json['notes'] as String?,
      isAnalyzedByAI: json['is_analyzed_by_ai'] as bool? ?? false,
      confidence: (json['confidence'] as num?)?.toDouble(),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'description': description,
      'brand': brand,
      'serving_size': servingSize,
      'serving_unit': servingUnit,
      'nutrition_data': nutritionData.toJson(),
      'image_path': imagePath,
      'meal_type': mealType,
      'consumed_at': consumedAt.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'notes': notes,
      'is_analyzed_by_ai': isAnalyzedByAI,
      'confidence': confidence,
    };
  }
  
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'description': description,
      'brand': brand,
      'serving_size': servingSize,
      'serving_unit': servingUnit,
      'nutrition_data': jsonEncode(nutritionData.toJson()),
      'image_path': imagePath,
      'meal_type': mealType,
      'consumed_at': consumedAt.millisecondsSinceEpoch,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'notes': notes,
      'is_analyzed_by_ai': isAnalyzedByAI ? 1 : 0,
      'confidence': confidence,
    };
  }
  
  factory FoodEntryModel.fromDatabase(Map<String, dynamic> map) {
    final nutritionDataJson = jsonDecode(map['nutrition_data'] as String) as Map<String, dynamic>;
    
    return FoodEntryModel(
      id: map['id'] as String,
      userId: map['user_id'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      brand: map['brand'] as String?,
      servingSize: (map['serving_size'] as num).toDouble(),
      servingUnit: map['serving_unit'] as String,
      nutritionData: NutritionData.fromJson(nutritionDataJson),
      imagePath: map['image_path'] as String?,
      mealType: map['meal_type'] as String,
      consumedAt: DateTime.fromMillisecondsSinceEpoch(map['consumed_at'] as int),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
      notes: map['notes'] as String?,
      isAnalyzedByAI: (map['is_analyzed_by_ai'] as int) == 1,
      confidence: (map['confidence'] as num?)?.toDouble(),
    );
  }
  
  FoodEntryModel copyWith({
    String? id,
    String? userId,
    String? name,
    String? description,
    String? brand,
    double? servingSize,
    String? servingUnit,
    NutritionData? nutritionData,
    String? imagePath,
    String? mealType,
    DateTime? consumedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? notes,
    bool? isAnalyzedByAI,
    double? confidence,
  }) {
    return FoodEntryModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      description: description ?? this.description,
      brand: brand ?? this.brand,
      servingSize: servingSize ?? this.servingSize,
      servingUnit: servingUnit ?? this.servingUnit,
      nutritionData: nutritionData ?? this.nutritionData,
      imagePath: imagePath ?? this.imagePath,
      mealType: mealType ?? this.mealType,
      consumedAt: consumedAt ?? this.consumedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      notes: notes ?? this.notes,
      isAnalyzedByAI: isAnalyzedByAI ?? this.isAnalyzedByAI,
      confidence: confidence ?? this.confidence,
    );
  }
}
