import '../../domain/entities/nutritional_goals.dart';

class NutritionalGoalsModel extends NutritionalGoals {
  const NutritionalGoalsModel({
    required super.id,
    required super.userId,
    required super.dailyCalories,
    required super.dailyProtein,
    required super.dailyCarbs,
    required super.dailyFats,
    required super.dailyFiber,
    required super.dailySugar,
    required super.dailySodium,
    required super.dailyVitaminA,
    required super.dailyVitaminC,
    required super.dailyVitaminD,
    required super.dailyCalcium,
    required super.dailyIron,
    required super.createdAt,
    required super.updatedAt,
  });
  
  factory NutritionalGoalsModel.fromEntity(NutritionalGoals entity) {
    return NutritionalGoalsModel(
      id: entity.id,
      userId: entity.userId,
      dailyCalories: entity.dailyCalories,
      dailyProtein: entity.dailyProtein,
      dailyCarbs: entity.dailyCarbs,
      dailyFats: entity.dailyFats,
      dailyFiber: entity.dailyFiber,
      dailySugar: entity.dailySugar,
      dailySodium: entity.dailySodium,
      dailyVitaminA: entity.dailyVitaminA,
      dailyVitaminC: entity.dailyVitaminC,
      dailyVitaminD: entity.dailyVitaminD,
      dailyCalcium: entity.dailyCalcium,
      dailyIron: entity.dailyIron,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }
  
  factory NutritionalGoalsModel.fromJson(Map<String, dynamic> json) {
    return NutritionalGoalsModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      dailyCalories: (json['daily_calories'] as num).toDouble(),
      dailyProtein: (json['daily_protein'] as num).toDouble(),
      dailyCarbs: (json['daily_carbs'] as num).toDouble(),
      dailyFats: (json['daily_fats'] as num).toDouble(),
      dailyFiber: (json['daily_fiber'] as num).toDouble(),
      dailySugar: (json['daily_sugar'] as num).toDouble(),
      dailySodium: (json['daily_sodium'] as num).toDouble(),
      dailyVitaminA: (json['daily_vitamin_a'] as num).toDouble(),
      dailyVitaminC: (json['daily_vitamin_c'] as num).toDouble(),
      dailyVitaminD: (json['daily_vitamin_d'] as num).toDouble(),
      dailyCalcium: (json['daily_calcium'] as num).toDouble(),
      dailyIron: (json['daily_iron'] as num).toDouble(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'daily_calories': dailyCalories,
      'daily_protein': dailyProtein,
      'daily_carbs': dailyCarbs,
      'daily_fats': dailyFats,
      'daily_fiber': dailyFiber,
      'daily_sugar': dailySugar,
      'daily_sodium': dailySodium,
      'daily_vitamin_a': dailyVitaminA,
      'daily_vitamin_c': dailyVitaminC,
      'daily_vitamin_d': dailyVitaminD,
      'daily_calcium': dailyCalcium,
      'daily_iron': dailyIron,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
  
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'user_id': userId,
      'daily_calories': dailyCalories,
      'daily_protein': dailyProtein,
      'daily_carbs': dailyCarbs,
      'daily_fats': dailyFats,
      'daily_fiber': dailyFiber,
      'daily_sugar': dailySugar,
      'daily_sodium': dailySodium,
      'daily_vitamin_a': dailyVitaminA,
      'daily_vitamin_c': dailyVitaminC,
      'daily_vitamin_d': dailyVitaminD,
      'daily_calcium': dailyCalcium,
      'daily_iron': dailyIron,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }
  
  factory NutritionalGoalsModel.fromDatabase(Map<String, dynamic> map) {
    return NutritionalGoalsModel(
      id: map['id'] as String,
      userId: map['user_id'] as String,
      dailyCalories: (map['daily_calories'] as num).toDouble(),
      dailyProtein: (map['daily_protein'] as num).toDouble(),
      dailyCarbs: (map['daily_carbs'] as num).toDouble(),
      dailyFats: (map['daily_fats'] as num).toDouble(),
      dailyFiber: (map['daily_fiber'] as num).toDouble(),
      dailySugar: (map['daily_sugar'] as num).toDouble(),
      dailySodium: (map['daily_sodium'] as num).toDouble(),
      dailyVitaminA: (map['daily_vitamin_a'] as num).toDouble(),
      dailyVitaminC: (map['daily_vitamin_c'] as num).toDouble(),
      dailyVitaminD: (map['daily_vitamin_d'] as num).toDouble(),
      dailyCalcium: (map['daily_calcium'] as num).toDouble(),
      dailyIron: (map['daily_iron'] as num).toDouble(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
    );
  }
  
  NutritionalGoalsModel copyWith({
    String? id,
    String? userId,
    double? dailyCalories,
    double? dailyProtein,
    double? dailyCarbs,
    double? dailyFats,
    double? dailyFiber,
    double? dailySugar,
    double? dailySodium,
    double? dailyVitaminA,
    double? dailyVitaminC,
    double? dailyVitaminD,
    double? dailyCalcium,
    double? dailyIron,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return NutritionalGoalsModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      dailyCalories: dailyCalories ?? this.dailyCalories,
      dailyProtein: dailyProtein ?? this.dailyProtein,
      dailyCarbs: dailyCarbs ?? this.dailyCarbs,
      dailyFats: dailyFats ?? this.dailyFats,
      dailyFiber: dailyFiber ?? this.dailyFiber,
      dailySugar: dailySugar ?? this.dailySugar,
      dailySodium: dailySodium ?? this.dailySodium,
      dailyVitaminA: dailyVitaminA ?? this.dailyVitaminA,
      dailyVitaminC: dailyVitaminC ?? this.dailyVitaminC,
      dailyVitaminD: dailyVitaminD ?? this.dailyVitaminD,
      dailyCalcium: dailyCalcium ?? this.dailyCalcium,
      dailyIron: dailyIron ?? this.dailyIron,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
