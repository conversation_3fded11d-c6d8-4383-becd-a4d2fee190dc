// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

import 'package:nutri_ai/main.dart';
import 'package:nutri_ai/data/datasources/database_helper.dart';
import 'package:nutri_ai/data/repositories/user_profile_repository_impl.dart';
import 'package:nutri_ai/data/repositories/food_entry_repository_impl.dart';
import 'package:nutri_ai/data/repositories/nutritional_goals_repository_impl.dart';
import 'package:nutri_ai/domain/usecases/create_user_profile_usecase.dart';
import 'package:nutri_ai/domain/usecases/get_daily_nutrition_usecase.dart';

void main() {
  testWidgets('App loads without crashing', (WidgetTester tester) async {
    // Initialize test dependencies
    SharedPreferences.setMockInitialValues({});
    final sharedPreferences = await SharedPreferences.getInstance();
    final databaseHelper = DatabaseHelper();
    final httpClient = http.Client();

    // Initialize repositories
    final userProfileRepository = UserProfileRepositoryImpl(databaseHelper);
    final foodEntryRepository = FoodEntryRepositoryImpl(databaseHelper);
    final nutritionalGoalsRepository = NutritionalGoalsRepositoryImpl(
      databaseHelper,
    );

    // Initialize use cases
    final createUserProfileUseCase = CreateUserProfileUseCase(
      userProfileRepository,
      nutritionalGoalsRepository,
    );

    final getDailyNutritionUseCase = GetDailyNutritionUseCase(
      foodEntryRepository,
      nutritionalGoalsRepository,
    );

    // Build our app and trigger a frame.
    await tester.pumpWidget(
      NutriAIApp(
        sharedPreferences: sharedPreferences,
        userProfileRepository: userProfileRepository,
        foodEntryRepository: foodEntryRepository,
        nutritionalGoalsRepository: nutritionalGoalsRepository,
        createUserProfileUseCase: createUserProfileUseCase,
        getDailyNutritionUseCase: getDailyNutritionUseCase,
        httpClient: httpClient,
      ),
    );

    // Verify that the app loads
    expect(find.byType(MaterialApp), findsOneWidget);
  });
}
